import 'dart:async';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/constants.dart';
import '../../constants/text_style.dart';
import '../../reusableWidgets/appbar.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  Map<int, bool> notificationStatus = {}; // To track notification status
  StreamSubscription? _streamSubscription;

  bool hasNewNotifications = false;

  int? loggedInUserId;
  final ScrollController _currentReadScrollController = ScrollController();
  bool isLoading = false;
  MessageController? messageController;

  @override
  void initState() {
    messageController = Provider.of<MessageController>(context, listen: false);
    _currentReadScrollController.addListener(_currentReadOnScroll);
    _initializeUserIdAndFetchData();

    super.initState();
  }

  Future _initializeUserIdAndFetchData() async {
    setState(() {
      isLoading = true;
    });

    await _initializeUserId();

    // Initialize Firebase stream for user clubs
    if (loggedInUserId != null) {
      await messageController?.initializeUserClubsStream(loggedInUserId!);
    }

    setState(() {
      isLoading = false;
      isInitialized = true;
    });
  }

  Future<void> _initializeUserId() async {
    loggedInUserId = await CommonHelper.getLoggedInUserId();
  }

  void _currentReadOnScroll() {
    // Scroll listener for pagination - currently not needed with Firebase streams
    // but keeping for potential future use
  }

  bool isInitialized = false;

  @override
  void dispose() {
    _currentReadScrollController.dispose();
    _streamSubscription?.cancel();

    super.dispose();
  }

  bool? isEmptyData;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const AppBarWidget(
            appBarText: 'Messages',
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Consumer<MessageController>(
          builder: (context, messageController, child) {
            final clubs = messageController.userClubsList;
            if (messageController.isFirstLoading) {
              return _buildSkeletonLoader();
            } else {
              if (clubs.isEmpty) {
                isEmptyData = true;
              } else {
                isEmptyData = false;
              }

              if (isEmptyData ?? false) {
                return Column(
                  children: [
                    Container(
                      margin:
                          const EdgeInsets.only(top: 25, left: 20, right: 20),
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        border: Border.all(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      child: ListTile(
                        titleTextStyle: lbBold.copyWith(
                          fontSize: 14,
                          height: 1.3,
                        ),
                        title: const Text(
                          "When you join or start a club, you’ll be able to message fellow club members here to coordinate meeting times, chat, etc.\n\nGet into a club & start chatting with your book people!",
                          // overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Skeletonizer(
                    effect: const SoldColorEffect(
                      color: AppConstants.skeletonforgroundColor,
                      lowerBound: 0.1,
                      upperBound: 0.5,
                    ),
                    enabled: messageController.isFirstLoading || clubs.isEmpty,
                    child: Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            controller: _currentReadScrollController,
                            padding: const EdgeInsets.only(bottom: 25),
                            itemCount: clubs.length,
                            itemBuilder: (context, index) {
                              int bookClubId = clubs[index]['bookClubId'] ?? 0;

                              return Skeleton.replace(
                                replacement: NetworkAwareTap(
                                  onTap: () => _handleChatTap(
                                      clubs[index]['bookClubId'].toString(),
                                      clubs[index]['bookClubId']),
                                  child: Stack(
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(top: 25),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 15),
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                clubs[index]['bookClubName'] ??
                                                    '',
                                                overflow: TextOverflow.ellipsis,
                                                style: lbBold.copyWith(
                                                  fontSize: 18,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Image.asset(
                                              "assets/icons/Messages.png",
                                              height: 30,
                                              width: 48,
                                              filterQuality: FilterQuality.high,
                                              fit: BoxFit.contain,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Visibility(
                                        visible: messageController
                                                    .notificationStatus[
                                                bookClubId] ==
                                            false,
                                        child: Positioned(
                                          top: 15,
                                          right: 2,
                                          child: Image.asset(
                                            AppConstants.notificationImagePath,
                                            height: 18,
                                            width: 18,
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                child: NetworkAwareTap(
                                  // onTap: () async {
                                  //   messageController.markMessageAsSeen(
                                  //       bookClubId.toString(),
                                  //       bookClubId,
                                  //       loggedInUserId ?? 0,
                                  //       context);
                                  //   if (context.mounted) {
                                  //     context.pushNamed(
                                  //       'chat-screen',
                                  //       queryParameters: {
                                  //         "userId": loggedInUserId.toString(),
                                  //         'bookClubId':
                                  //             clubs[index]['bookClubId'].toString(),
                                  //       },
                                  //     );
                                  //   }
                                  // },
                                  onTap: () => _handleChatTap(
                                      clubs[index]['bookClubId'].toString(),
                                      clubs[index]['bookClubId']),
                                  child: Stack(
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(top: 25),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 15),
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                clubs[index]['bookClubName'] ??
                                                    '',
                                                overflow: TextOverflow.ellipsis,
                                                style: lbBold.copyWith(
                                                  fontSize: 18,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Image.asset(
                                              "assets/icons/Messages.png",
                                              height: 30,
                                              width: 48,
                                              filterQuality: FilterQuality.high,
                                              fit: BoxFit.contain,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Visibility(
                                        visible: messageController
                                                    .notificationStatus[
                                                bookClubId] ==
                                            false,
                                        child: Positioned(
                                          top: 15,
                                          right: 2,
                                          child: Image.asset(
                                            AppConstants.notificationImagePath,
                                            height: 18,
                                            width: 18,
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
            }
          },
        ),
      ),
    );
  }

// Skeleton Loader
  Widget _buildSkeletonLoader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Skeleton.replace(
        replacement: ListView.builder(
          itemCount: 10, // Number of skeleton items to show
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(top: 25),
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            );
          },
        ),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 25, left: 20, right: 20),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                border: Border.all(
                  color: AppConstants.primaryColor,
                  width: 1.5,
                ),
              ),
              child: ListTile(
                titleTextStyle: lbBold.copyWith(
                  fontSize: 14,
                  height: 1.3,
                ),
                title: const Text(
                  "When you join or start a club, you’ll be able to message fellow club members here to coordinate meeting times, chat, etc.\n\nGet into a club & start chatting with your book people!",
                  // overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleChatTap(String clubId, int userClubId) async {
    // Navigate to chat screen
    if (mounted) {
      context.push('/chat-screen?bookClubId=$clubId').then((_) {
        if (mounted) {
          Provider.of<MessageController>(context, listen: false)
              .markMessageAsSeen(
            clubId,
            userClubId,
            loggedInUserId ?? 0,
            context,
          );
        }
      });
    }
  }
}

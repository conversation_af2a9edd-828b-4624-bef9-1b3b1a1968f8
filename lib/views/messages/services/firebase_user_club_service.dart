import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:eljunto/constants/common_helper.dart';

/// Service for handling Firebase Firestore user club data streams
/// Replaces API-based club data fetching with real-time Firebase streams
class FirebaseUserClubService {
  static final FirebaseUserClubService _instance =
      FirebaseUserClubService._internal();
  factory FirebaseUserClubService() => _instance;
  FirebaseUserClubService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  StreamSubscription<DocumentSnapshot>? _userClubsSubscription;

  /// Stream of user's club data from Firebase
  /// Structure: users/{userId} document with individual club fields
  /// Based on screenshot: bookClubId, bookClubName, bookAuthor, createdAt, userId fields
  Stream<List<Map<String, dynamic>>> getUserClubsStream(int userId) {
    try {
      return _firestore
          .collection('users')
          .doc(userId.toString())
          .snapshots()
          .map((docSnapshot) {
        if (!docSnapshot.exists) {
          log('User document does not exist for userId: $userId');
          return <Map<String, dynamic>>[];
        }

        final data = docSnapshot.data();
        if (data == null) {
          log('No data found for userId: $userId');
          return <Map<String, dynamic>>[];
        }

        // Extract club data - handle multiple possible structures
        List<Map<String, dynamic>> clubs = [];

        // Method 1: Check if there's a 'clubs' array field
        if (data.containsKey('clubs') && data['clubs'] is List) {
          final clubsList = data['clubs'] as List;
          for (var club in clubsList) {
            if (club is Map<String, dynamic>) {
              clubs.add(club);
            }
          }
        }
        // Method 2: Check for individual club fields in the document
        else if (data.containsKey('bookClubId') &&
            data.containsKey('bookClubName')) {
          final clubData = <String, dynamic>{
            'bookClubId': data['bookClubId'],
            'bookClubName': data['bookClubName'],
          };

          // Add optional fields if they exist
          if (data.containsKey('bookAuthor')) {
            clubData['bookAuthor'] = data['bookAuthor'];
          }
          if (data.containsKey('createdAt')) {
            clubData['createdAt'] = data['createdAt'];
          }
          if (data.containsKey('userId')) {
            clubData['userId'] = data['userId'];
          }

          clubs.add(clubData);
        }
        // Method 3: Look for numbered club fields (e.g., club_1, club_2, etc.)
        else {
          data.forEach((key, value) {
            if (value is Map<String, dynamic>) {
              clubs.add(value);
            }
          });
        }

        log('Extracted ${clubs.length} clubs for userId: $userId');
        log('Raw document data keys: ${data.keys.toList()}');
        if (clubs.isNotEmpty) {
          log('First club data: ${clubs.first}');
        }
        return clubs;
      });
    } catch (e) {
      log('Error creating user clubs stream: $e');
      return Stream.value(<Map<String, dynamic>>[]);
    }
  }

  /// Get current user's club data as a one-time fetch
  Future<List<Map<String, dynamic>>> getCurrentUserClubs() async {
    try {
      final userId = await CommonHelper.getLoggedInUserId();
      if (userId == null) {
        log('No logged in user found');
        return [];
      }

      final docSnapshot =
          await _firestore.collection('users').doc(userId.toString()).get();

      if (!docSnapshot.exists) {
        log('User document does not exist for userId: $userId');
        return [];
      }

      final data = docSnapshot.data();
      if (data == null) {
        log('No data found for userId: $userId');
        return [];
      }

      // Extract club data using the same logic as the stream
      List<Map<String, dynamic>> clubs = [];

      // Method 1: Check if there's a 'clubs' array field
      if (data.containsKey('clubs') && data['clubs'] is List) {
        final clubsList = data['clubs'] as List;
        for (var club in clubsList) {
          if (club is Map<String, dynamic>) {
            clubs.add(club);
          }
        }
      }
      // Method 2: Check for individual club fields in the document
      else if (data.containsKey('bookClubId') &&
          data.containsKey('bookClubName')) {
        final clubData = <String, dynamic>{
          'bookClubId': data['bookClubId'],
          'bookClubName': data['bookClubName'],
        };

        // Add optional fields if they exist
        if (data.containsKey('bookAuthor')) {
          clubData['bookAuthor'] = data['bookAuthor'];
        }
        if (data.containsKey('createdAt')) {
          clubData['createdAt'] = data['createdAt'];
        }
        if (data.containsKey('userId')) {
          clubData['userId'] = data['userId'];
        }

        clubs.add(clubData);
      }
      // Method 3: Look for numbered club fields
      else {
        data.forEach((key, value) {
          if (key.startsWith('club_') && value is Map<String, dynamic>) {
            clubs.add(value);
          }
        });
      }

      log('getCurrentUserClubs - Extracted ${clubs.length} clubs');
      log('getCurrentUserClubs - Raw document data keys: ${data.keys.toList()}');
      return clubs;
    } catch (e) {
      log('Error fetching current user clubs: $e');
      return [];
    }
  }

  /// Dispose of any active subscriptions
  void dispose() {
    _userClubsSubscription?.cancel();
    _userClubsSubscription = null;
  }

  /// Check if user has any clubs
  Future<bool> hasUserClubs() async {
    final clubs = await getCurrentUserClubs();
    return clubs.isNotEmpty;
  }

  /// Get club IDs from user's clubs
  Future<List<int>> getUserClubIds() async {
    final clubs = await getCurrentUserClubs();
    return clubs
        .map((club) => club['bookClubId'] as int?)
        .where((id) => id != null)
        .cast<int>()
        .toList();
  }
}
